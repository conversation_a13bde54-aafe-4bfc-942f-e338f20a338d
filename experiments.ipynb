import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler,LabelEncoder
import pickle

#loading the dataset

data=pd.read_csv('Churn_Modelling.csv')
data.head()

data=data.drop(['RowNumber','CustomerId','Surname'],axis=1)
data.head()

label_encoder_gender= LabelEncoder()
data['Gender']=label_encoder_gender.fit_transform(data['Gender'])
data


from sklearn.preprocessing import OneHotEncoder
onehot_encoder_geo=OneHotEncoder()
geo_encoder=onehot_encoder_geo.fit_transform(data[['Geography']]).toarray()
geo_encoder


onehot_encoder_geo.get_feature_names_out(['Geography'])

geo_encoded_df=pd.DataFrame(geo_encoder,columns=onehot_encoder_geo.get_feature_names_out(['Geography']))
geo_encoded_df

data = pd.concat([data.drop('Geography',axis=1),geo_encoded_df],axis=1)
data

with open('label_encoder_gender.pkl','wb') as file:
    pickle.dump(label_encoder_gender,file)

with open('onehot_encoder_geo.pkl','wb') as file:
    pickle.dump(onehot_encoder_geo,file)
    

data.head()

x=data.drop('Exited',axis=1)
y=data['Exited']

x_train,x_test,y_train,y_test=train_test_split(x,y,test_size=0.2,random_state=42)

scaler=StandardScaler()
x_train=scaler.fit_transform(x_train)
x_test=scaler.transform(x_test)

x_train

with open('scaler.pkl','wb') as file:
    pickle.dump(scaler,file)

data

import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense
from tensorflow.keras.callbacks import EarlyStopping,TensorBoard
import datetime



(x_train.shape[1])

#building ANN module
model=Sequential([
    Dense(64,activation='relu',input_shape=(x_train.shape[1],)),
    Dense(32,activation='relu'),
    Dense(1,activation='sigmoid')
])

model.summary()

import tensorflow
opt=tensorflow.keras.optimizers.Adam(learning_rate=0.01)
loss=tensorflow.keras.losses.BinaryCrossentropy()
loss

model.compile(optimizer=opt,loss='binary_crossentropy',metrics=['accuracy'])


from tensorflow.keras.callbacks import EarlyStopping,TensorBoard

log_dir="logs/fit/"+datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
tensorflow_callback=TensorBoard(log_dir=log_dir,histogram_freq=1)

early_stopping_callback=EarlyStopping(monitor='val_loss',patience=10)

history=model.fit(
    x_train,
    y_train,
    epochs=100,
    validation_data=(x_test,y_test),
    callbacks=[early_stopping_callback,tensorflow_callback]
)

model.save('model.h5')

## load tensorboard
%load_ext tensorboard
%tensorboard --logdir logs/fit

